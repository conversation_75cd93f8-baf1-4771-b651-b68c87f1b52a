package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 仪表盘数据实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class DashboardData(
    @Id
    val id: String = randomId(),

    @Column(length = 50)
    val type: String, // 'stat' 或 'activity'

    @Column(length = 100)
    val title: String,

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column()
    val value: String,

    @Column(length = 50)
    val icon: String? = null,

    @Column(length = 50)
    val color: String? = null,

    @Column()
    val sortOrder: Int = 0,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
