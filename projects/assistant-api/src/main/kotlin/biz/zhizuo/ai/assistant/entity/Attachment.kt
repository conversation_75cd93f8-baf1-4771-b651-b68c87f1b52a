package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 附件实体
 */
@Entity
@Table()
@Comment("附件实体")
@EntityListeners(AuditingEntityListener::class)
data class Attachment(
    @Id
    val id: String = randomId(),

    @ManyToOne
    @Comment("关联的消息")
    val message: ChatMessage,

    @Column()
    @Comment("附件名称")
    val name: String,

    @Enumerated(EnumType.STRING)
    @Column()
    @Comment("附件类型")
    val type: AttachmentType,

    @Column()
    @Comment("文件大小（字节）")
    val fileSize: Long? = null,

    @Column()
    @Comment("MIME类型")
    val mimeType: String? = null,

    @Column()
    @Comment("文件存储路径")
    val filePath: String? = null,

    @Column(columnDefinition = "TEXT")
    @Comment("附件内容（文本类型）")
    val content: String? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)

/**
 * 附件类型枚举
 */
enum class AttachmentType {
    IMAGE, DOCUMENT, TEXT, URL, OTHER
}
