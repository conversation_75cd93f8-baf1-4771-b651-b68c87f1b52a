package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 附件实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class Attachment(
    @Id
    val id: String = randomId(),

    @ManyToOne
    val message: ChatMessage,

    @Column()
    val name: String,

    @Enumerated(EnumType.STRING)
    @Column()
    val type: AttachmentType,

    @Column()
    val fileSize: Long? = null,

    @Column()
    val mimeType: String? = null,

    @Column()
    val filePath: String? = null,

    @Column(columnDefinition = "TEXT")
    val content: String? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)

/**
 * 附件类型枚举
 */
enum class AttachmentType {
    IMAGE, DOCUMENT, TEXT, URL, OTHER
}
