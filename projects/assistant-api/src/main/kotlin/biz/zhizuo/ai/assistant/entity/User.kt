package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 用户实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class User(
    @Id
    val id: String = randomId(),

    @Column(unique = true, length = 50)
    val username: String,

    @Column(unique = true, length = 100)
    val email: String,

    @Column()
    val password: String,

    @Column()
    val twoFactorEnabled: Boolean = false,

    @Column()
    val twoFactorSecret: String? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
