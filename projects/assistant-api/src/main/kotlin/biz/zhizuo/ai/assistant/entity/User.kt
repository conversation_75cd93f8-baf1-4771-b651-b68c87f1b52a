package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 用户实体
 */
@Entity
@Table()
@Comment("用户实体")
@EntityListeners(AuditingEntityListener::class)
data class User(
    @Id
    val id: String = randomId(),

    @Column(unique = true, length = 50)
    @Comment("用户名")
    val username: String,

    @Column(unique = true, length = 100)
    @Comment("邮箱地址")
    val email: String,

    @Column()
    @Comment("加密后的密码")
    val password: String,

    @Column()
    @Comment("是否启用双因子认证")
    val twoFactorEnabled: Boolean = false,

    @Column()
    @Comment("双因子认证密钥")
    val twoFactorSecret: String? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
