package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.config.FloatArrayConverter
import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * AI助手代理聚合根实体
 * 代表一个AI助手代理，如日程助理、美工、活动策划等
 */
@Entity
@EntityListeners(AuditingEntityListener::class)
data class Agent(
    @Id
    val id: String = randomId(),

    @Column(unique = true)
    @Comment("给程序判断用的编码值")
    val code: String,

    @Column(length = 100, unique = true)
    @Comment("显示给用户看的 Agent 名称")
    val name: String,

    @Column(length = 4096)
    val description: String,

    @Column(length = 500)
    val avatarUrl: String? = null, // Agent头像URL

    @Column(length = 65535)
    @Convert(converter = FloatArrayConverter::class)
    var embeddingVector: FloatArray? = null, // Agent的嵌入向量

    val isActive: Boolean = true,

    val confidenceThreshold: Double = 0.7, // Agent匹配的置信度阈值

    val priorityOrder: Int = 0, // 优先级，数字越小优先级越高

    val monthlyPrice: Int = 0, // 月订阅价格（分）

    val isDefault: Boolean = false, // 是否为默认Agent（免费）

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,

    @OneToMany(mappedBy = "agent", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    val executionPlans: List<ExecutionPlan> = emptyList(),
)
