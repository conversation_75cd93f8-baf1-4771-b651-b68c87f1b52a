package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 会话Agent关联实体
 */
@Entity
@EntityListeners(AuditingEntityListener::class)
data class SessionAgent(
    @Id
    val id: String = randomId(),

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn()
    val session: ChatSession,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn()
    val agent: Agent,

    @Column()
    val isActive: Boolean = true, // 是否在当前会话中激活

    @Column()
    val addedAt: LocalDateTime = LocalDateTime.now(),

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)
