package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 会话Agent关联实体
 */
@Entity
@Comment("会话Agent关联实体")
@EntityListeners(AuditingEntityListener::class)
data class SessionAgent(
    @Id
    val id: String = randomId(),

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn()
    @Comment("关联的聊天会话")
    val session: ChatSession,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn()
    @Comment("关联的Agent")
    val agent: Agent,

    @Column()
    @Comment("是否在当前会话中激活")
    val isActive: Boolean = true,

    @Column()
    @Comment("添加到会话的时间")
    val addedAt: LocalDateTime = LocalDateTime.now(),

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
