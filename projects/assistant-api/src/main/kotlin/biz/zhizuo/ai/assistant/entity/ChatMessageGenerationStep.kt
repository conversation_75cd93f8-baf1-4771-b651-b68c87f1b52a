package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 聊天消息生成步骤实体
 */
@Entity
@Table()
@Comment("聊天消息生成步骤实体")
@EntityListeners(AuditingEntityListener::class)
data class ChatMessageGenerationStep(
    @Id
    val id: String = randomId(),

    @ManyToOne
    @Comment("关联的消息")
    val message: ChatMessage,

    @Column()
    @Comment("步骤名称")
    val step: String,

    @Column(columnDefinition = "TEXT")
    @Comment("步骤描述")
    val description: String? = null,

    @Column()
    @Comment("步骤顺序")
    val stepOrder: Int,

    @Enumerated(EnumType.STRING)
    @Column()
    @Comment("步骤状态")
    val status: StepStatus = StepStatus.PENDING,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
