package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*

/**
 * 聊天消息生成步骤实体
 */
@Entity
@Table()
data class ChatMessageGenerationStep(
    @Id
    val id: String = randomId(),

    @ManyToOne
    val message: ChatMessage,

    @Column()
    val step: String,

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column()
    val stepOrder: Int,

    @Enumerated(EnumType.STRING)
    @Column()
    val status: StepStatus = StepStatus.PENDING,
)
