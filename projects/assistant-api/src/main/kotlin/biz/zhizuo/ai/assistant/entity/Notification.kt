package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 消息实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class Notification(
    @Id
    val id: String = randomId(),

    @Column()
    val userId: String? = null,

    @Column(length = 50)
    val type: String, // 'notification' 或 'private'

    @Column(length = 50)
    val category: String? = null,

    @Column(length = 200)
    val title: String,

    @Column(columnDefinition = "TEXT")
    val content: String? = null,

    @Column()
    val isRead: Boolean = false,

    @Column(length = 50)
    val icon: String? = null,

    @Column(length = 50)
    val color: String? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
