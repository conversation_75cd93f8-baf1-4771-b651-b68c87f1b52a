package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 聊天消息反馈实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class ChatMessageFeedback(
    @Id
    val id: String = randomId(),

    @OneToOne
    val message: ChatMessage,

    val rate: Int,

    @Column(columnDefinition = "TEXT")
    val content: String? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)
