package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 收藏实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class Favorite(
    @Id
    val id: String = randomId(),

    @Column()
    val userId: String,

    @Column(length = 50)
    val type: String, // 'article' 或 'video'

    @Column(length = 200)
    val title: String,

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(length = 500)
    val url: String? = null,

    @Column(length = 500)
    val image: String? = null,

    @Column(length = 100)
    val author: String? = null,

    @Column()
    val publishedDate: String? = null,

    @Column()
    val readTime: String? = null,

    @Column
    val duration: String? = null,

    @Column()
    val viewCount: String? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
