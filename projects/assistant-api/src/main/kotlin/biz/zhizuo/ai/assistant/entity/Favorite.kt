package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 收藏实体
 */
@Entity
@Table()
@Comment("收藏实体")
@EntityListeners(AuditingEntityListener::class)
data class Favorite(
    @Id
    val id: String = randomId(),

    @Column()
    @Comment("收藏用户ID")
    val userId: String,

    @Column(length = 50)
    @Comment("收藏类型：article或video")
    val type: String,

    @Column(length = 200)
    @Comment("收藏标题")
    val title: String,

    @Column(columnDefinition = "TEXT")
    @Comment("收藏描述")
    val description: String? = null,

    @Column(length = 500)
    @Comment("收藏链接")
    val url: String? = null,

    @Column(length = 500)
    @Comment("封面图片")
    val image: String? = null,

    @Column(length = 100)
    @Comment("作者")
    val author: String? = null,

    @Column()
    @Comment("发布日期")
    val publishedDate: String? = null,

    @Column()
    @Comment("阅读时长")
    val readTime: String? = null,

    @Column
    @Comment("视频时长")
    val duration: String? = null,

    @Column()
    @Comment("观看次数")
    val viewCount: String? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
