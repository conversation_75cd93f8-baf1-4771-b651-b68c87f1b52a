package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.CreationTimestamp
import java.time.OffsetDateTime

/**
 * Entity representing a record of a user selecting a suggestion.
 * This data is used to analyze suggestion effectiveness and improve future recommendations.
 */
@Entity
@Table()
data class SuggestionFeedback(
    @Id
    var id: String = randomId(),

    @Column(columnDefinition = "TEXT")
    var suggestionText: String,

    @Column()
    var isReportTemplate: Boolean = false,

    @Column()
    var messageId: String,

    @Column()
    var sessionId: String,

    @Column()
    var suggestionId: String,

    @Column() // 关联的用户ID，可以为空（如果建议是在非登录状态下提供和点击的）
    var userId: String? = null,

    @CreationTimestamp
    @Column(updatable = false)
    var createdAt: OffsetDateTime? = null,
)
