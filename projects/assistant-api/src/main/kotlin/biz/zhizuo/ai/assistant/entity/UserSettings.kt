package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 用户设置实体
 */
@Entity
@Table()
@Comment("用户设置实体")
@EntityListeners(AuditingEntityListener::class)
data class UserSettings(
    @Id
    val id: String = randomId(),

    @Column(unique = true)
    @Comment("关联的用户ID")
    val userId: String,

    @Column(length = 20)
    @Comment("主题模式：light、dark、auto")
    val themeMode: String = "auto",

    @Column(length = 10)
    @Comment("界面语言")
    val language: String = "zh-CN",

    @Column()
    @Comment("是否启用通知")
    val notificationsEnabled: Boolean = true,

    @Column()
    @Comment("是否启用邮件通知")
    val emailNotifications: Boolean = true,

    @Column()
    @Comment("是否启用推送通知")
    val pushNotifications: Boolean = true,

    @Column()
    @Comment("隐私设置：是否公开个人资料")
    val privacyProfilePublic: Boolean = true,

    @Column()
    @Comment("隐私设置：是否显示邮箱")
    val privacyShowEmail: Boolean = false,

    @Column()
    @Comment("隐私设置：是否显示活动")
    val privacyShowActivity: Boolean = true,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
