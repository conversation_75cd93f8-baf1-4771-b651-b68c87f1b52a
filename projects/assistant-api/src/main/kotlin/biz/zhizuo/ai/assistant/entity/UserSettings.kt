package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 用户设置实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class UserSettings(
    @Id
    val id: String = randomId(),

    @Column(unique = true)
    val userId: String,

    @Column(length = 20)
    val themeMode: String = "auto", // 'light', 'dark', 'auto'

    @Column(length = 10)
    val language: String = "zh-CN",

    @Column()
    val notificationsEnabled: Boolean = true,

    @Column()
    val emailNotifications: Boolean = true,

    @Column()
    val pushNotifications: Boolean = true,

    @Column()
    val privacyProfilePublic: Boolean = true,

    @Column()
    val privacyShowEmail: Boolean = false,

    @Column()
    val privacyShowActivity: Boolean = true,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
