package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 聊天消息实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class ChatMessage(
    @Id
    val id: String = randomId(),

    @ManyToOne
    val session: ChatSession,

    @ManyToOne
    val user: User,

    @Enumerated(EnumType.STRING)
    @Column()
    val role: MessageRole,

    @Column(columnDefinition = "TEXT")
    val content: String,

    @Column()
    val isGenerating: Boolean = false,

    @ManyToOne
    val replyTo: ChatMessage? = null,

    @OneToMany(mappedBy = "replyTo")
    val replies: List<ChatMessage> = emptyList(),

    @OneToOne
    var activeReply: ChatMessage? = null,

    val isArchived: Boolean = false,

    @Column()
    val assistantName: String? = null,

    @Column()
    val liked: Boolean? = null,

    @Column()
    val disliked: Boolean? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @OneToMany(mappedBy = "message", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    val steps: List<ChatMessageGenerationStep> = emptyList(),

    @OneToMany(mappedBy = "message", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    val attachments: List<Attachment> = emptyList(),

    @OneToOne(mappedBy = "message", cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    val feedback: ChatMessageFeedback? = null,
)

/**
 * 消息角色枚举
 */
enum class MessageRole {
    USER,
    ASSISTANT,
    SYSTEM,
}
