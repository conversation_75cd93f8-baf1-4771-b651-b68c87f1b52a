package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 聊天会话实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class ChatSession(
    @Id
    val id: String = randomId(),

    @ManyToOne
    val user: User,

    @Column(length = 200)
    val name: String,

    @Column()
    val pinned: Boolean = false,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,

    @Column()
    val lastMessageAt: LocalDateTime? = null,
) {
    @OneToMany(mappedBy = "session")
    val messages: List<ChatMessage> = emptyList()
}
