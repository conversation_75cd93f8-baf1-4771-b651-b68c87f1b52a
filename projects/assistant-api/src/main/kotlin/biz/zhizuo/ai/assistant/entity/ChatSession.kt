package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 聊天会话实体
 */
@Entity
@Table()
@Comment("聊天会话实体")
@EntityListeners(AuditingEntityListener::class)
data class ChatSession(
    @Id
    val id: String = randomId(),

    @ManyToOne
    @Comment("会话所属用户")
    val user: User,

    @Column(length = 200)
    @Comment("会话名称")
    val name: String,

    @Column()
    @Comment("是否置顶")
    val pinned: Boolean = false,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,

    @Column()
    @Comment("最后一条消息时间")
    val lastMessageAt: LocalDateTime? = null,
) {
    @OneToMany(mappedBy = "session")
    @Comment("会话中的所有消息")
    val messages: List<ChatMessage> = emptyList()
}
