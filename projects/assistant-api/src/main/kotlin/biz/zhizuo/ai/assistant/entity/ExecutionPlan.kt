package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.config.FloatArrayConverter
import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime


/**
 * 执行计划实体
 * 属于某个Agent的具体执行方案
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class ExecutionPlan(
    @Id
    val id: String = randomId(),

    @Column(length = 100)
    val name: String,

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column(length = 65535)
    @Convert(converter = FloatArrayConverter::class)
    var embeddingVector: FloatArray? = null, // 执行计划的嵌入向量

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn()
    val agent: Agent, // 所属Agent

    @Column()
    val isActive: Boolean = true,

    @Column()
    val priorityOrder: Int = 0, // 优先级，数字越小优先级越高

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @OneToMany(mappedBy = "executionPlan", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val steps: List<ExecutionPlanStep> = emptyList(),
)

/**
 * 执行计划步骤实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class ExecutionPlanStep(
    @Id
    val id: String = randomId(),

    @ManyToOne
    @JoinColumn()
    val executionPlan: ExecutionPlan,

    @Column(length = 100)
    val name: String, // 步骤名称，如：理解问题、搜集资料等

    @Column(columnDefinition = "TEXT")
    val description: String? = null,

    @Column()
    val stepOrder: Int, // 步骤顺序

    @Column(length = 100)
    val modelName: String? = null, // 使用的模型名称

    @Column(columnDefinition = "TEXT")
    val promptTemplate: String? = null, // 提示词模板

    @Column()
    val expectedDurationSeconds: Int? = null, // 预期执行时长（秒）

    @Column()
    val isParallel: Boolean = false, // 是否可以并行执行

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)

/**
 * 消息执行上下文实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class MessageExecutionContext(
    @Id
    val id: String = randomId(),

    @OneToOne
    @JoinColumn()
    val message: ChatMessage,

    @ManyToOne
    @JoinColumn()
    val executionPlan: ExecutionPlan,

    @Column(length = 50)
    val detectedDomain: String, // 检测到的问题领域

    @Column()
    val confidenceScore: Double = 0.0, // 匹配置信度

    @Enumerated(EnumType.STRING)
    @Column()
    val status: ExecutionStatus = ExecutionStatus.PENDING,

    @Column()
    val currentStepOrder: Int = 0,

    @Column()
    val startedAt: LocalDateTime? = null,

    @Column()
    val completedAt: LocalDateTime? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @OneToMany(mappedBy = "executionContext", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val stepExecutions: List<StepExecution> = emptyList(),
)

/**
 * 步骤执行记录实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class StepExecution(
    @Id
    val id: String = randomId(),

    @ManyToOne
    @JoinColumn()
    val executionContext: MessageExecutionContext,

    @ManyToOne
    @JoinColumn()
    val executionPlanStep: ExecutionPlanStep,

    @Enumerated(EnumType.STRING)
    @Column()
    val status: StepStatus = StepStatus.PENDING,

    @Column()
    val startedAt: LocalDateTime? = null,

    @Column()
    val completedAt: LocalDateTime? = null,

    @Column()
    val durationSeconds: Int? = null,

    @Column(columnDefinition = "TEXT")
    val output: String? = null, // 步骤输出内容

    @Column(columnDefinition = "TEXT")
    val errorMessage: String? = null, // 错误信息

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)


