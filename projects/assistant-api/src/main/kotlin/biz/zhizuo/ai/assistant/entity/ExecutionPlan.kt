package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.config.FloatArrayConverter
import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime


/**
 * 执行计划实体
 * 属于某个Agent的具体执行方案
 */
@Entity
@Table()
@Comment("执行计划实体")
@EntityListeners(AuditingEntityListener::class)
data class ExecutionPlan(
    @Id
    val id: String = randomId(),

    @Column(length = 100)
    @Comment("执行计划名称")
    val name: String,

    @Column(columnDefinition = "TEXT")
    @Comment("执行计划详细描述")
    val description: String? = null,

    @Column(length = 65535)
    @Convert(converter = FloatArrayConverter::class)
    @Comment("执行计划的嵌入向量，用于语义匹配")
    var embeddingVector: FloatArray? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn()
    @Comment("所属Agent")
    val agent: Agent,

    @Column()
    @Comment("是否激活状态")
    val isActive: Boolean = true,

    @Column()
    @Comment("优先级，数字越小优先级越高")
    val priorityOrder: Int = 0,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,

    @OneToMany(mappedBy = "executionPlan", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val steps: List<ExecutionPlanStep> = emptyList(),
)

/**
 * 执行计划步骤实体
 */
@Entity
@Table()
@Comment("执行计划步骤实体")
@EntityListeners(AuditingEntityListener::class)
data class ExecutionPlanStep(
    @Id
    val id: String = randomId(),

    @ManyToOne
    @JoinColumn()
    @Comment("所属执行计划")
    val executionPlan: ExecutionPlan,

    @Column(length = 100)
    @Comment("步骤名称，如：理解问题、搜集资料等")
    val name: String,

    @Column(columnDefinition = "TEXT")
    @Comment("步骤详细描述")
    val description: String? = null,

    @Column()
    @Comment("步骤顺序")
    val stepOrder: Int,

    @Column(length = 100)
    @Comment("使用的模型名称")
    val modelName: String? = null,

    @Column(columnDefinition = "TEXT")
    @Comment("提示词模板")
    val promptTemplate: String? = null,

    @Column()
    @Comment("预期执行时长（秒）")
    val expectedDurationSeconds: Int? = null,

    @Column()
    @Comment("是否可以并行执行")
    val isParallel: Boolean = false,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)

/**
 * 消息执行上下文实体
 */
@Entity
@Table()
@Comment("消息执行上下文实体")
@EntityListeners(AuditingEntityListener::class)
data class MessageExecutionContext(
    @Id
    val id: String = randomId(),

    @OneToOne
    @JoinColumn()
    @Comment("关联的聊天消息")
    val message: ChatMessage,

    @ManyToOne
    @JoinColumn()
    @Comment("选择的执行计划")
    val executionPlan: ExecutionPlan,

    @Column(length = 50)
    @Comment("检测到的问题领域")
    val detectedDomain: String,

    @Column()
    @Comment("匹配置信度")
    val confidenceScore: Double = 0.0,

    @Enumerated(EnumType.STRING)
    @Column()
    @Comment("执行状态")
    val status: ExecutionStatus = ExecutionStatus.PENDING,

    @Column()
    @Comment("当前执行步骤顺序")
    val currentStepOrder: Int = 0,

    @Column()
    @Comment("开始执行时间")
    val startedAt: LocalDateTime? = null,

    @Column()
    @Comment("完成执行时间")
    val completedAt: LocalDateTime? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,

    @OneToMany(mappedBy = "executionContext", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    val stepExecutions: List<StepExecution> = emptyList(),
)

/**
 * 步骤执行记录实体
 */
@Entity
@Table()
@Comment("步骤执行记录实体")
@EntityListeners(AuditingEntityListener::class)
data class StepExecution(
    @Id
    val id: String = randomId(),

    @ManyToOne
    @JoinColumn()
    @Comment("关联的执行上下文")
    val executionContext: MessageExecutionContext,

    @ManyToOne
    @JoinColumn()
    @Comment("关联的执行计划步骤")
    val executionPlanStep: ExecutionPlanStep,

    @Enumerated(EnumType.STRING)
    @Column()
    @Comment("步骤执行状态")
    val status: StepStatus = StepStatus.PENDING,

    @Column()
    @Comment("步骤开始时间")
    val startedAt: LocalDateTime? = null,

    @Column()
    @Comment("步骤完成时间")
    val completedAt: LocalDateTime? = null,

    @Column()
    @Comment("执行耗时（秒）")
    val durationSeconds: Int? = null,

    @Column(columnDefinition = "TEXT")
    @Comment("步骤输出内容")
    val output: String? = null,

    @Column(columnDefinition = "TEXT")
    @Comment("错误信息")
    val errorMessage: String? = null,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)


