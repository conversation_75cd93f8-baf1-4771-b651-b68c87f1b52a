package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 用户资料实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class UserProfile(
    @Id
    val id: String = randomId(),

    @Column(unique = true)
    val userId: String,

    @Column(length = 100)
    val name: String,

    @Column(length = 255)
    val avatar: String? = null,

    @Column(length = 100)
    val email: String,

    @Column()
    val joinDate: LocalDate? = null,

    @Column(columnDefinition = "TEXT")
    val bio: String? = null,

    @Column()
    val postsCount: Int = 0,

    @Column()
    val followersCount: Int = 0,

    @Column()
    val followingCount: Int = 0,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
