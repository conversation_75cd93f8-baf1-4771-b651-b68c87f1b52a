package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 帮助信息实体
 */
@Entity
@Table()
@Comment("帮助信息实体")
@EntityListeners(AuditingEntityListener::class)
data class HelpInfo(
    @Id
    val id: String = randomId(),

    @Column(length = 50)
    @Comment("帮助类型：faq或category")
    val type: String,

    @Column(length = 200)
    @Comment("帮助标题")
    val title: String,

    @Column(columnDefinition = "TEXT")
    @Comment("帮助内容")
    val content: String? = null,

    @Column(length = 100)
    @Comment("帮助分类")
    val category: String? = null,

    @Column(length = 50)
    @Comment("图标名称")
    val icon: String? = null,

    @Column()
    @Comment("排序顺序")
    val sortOrder: Int = 0,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
