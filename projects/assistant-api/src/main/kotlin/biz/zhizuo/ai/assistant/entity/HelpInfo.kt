package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 帮助信息实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class HelpInfo(
    @Id
    val id: String = randomId(),

    @Column(length = 50)
    val type: String, // 'faq' 或 'category'

    @Column(length = 200)
    val title: String,

    @Column(columnDefinition = "TEXT")
    val content: String? = null,

    @Column(length = 100)
    val category: String? = null,

    @Column(length = 50)
    val icon: String? = null,

    @Column()
    val sortOrder: Int = 0,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
