package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 用户Agent订阅实体
 */
@Entity
@EntityListeners(AuditingEntityListener::class)
data class UserAgentSubscription(
    @Id
    val id: String = randomId(),

    @ManyToOne(fetch = FetchType.LAZY)
    val user: User,

    @ManyToOne(fetch = FetchType.LAZY)
    val agent: Agent,

    val isActive: Boolean = true,

    val subscribedAt: LocalDateTime = LocalDateTime.now(),

    val expiresAt: LocalDateTime? = null, // 订阅到期时间，null表示永久

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
