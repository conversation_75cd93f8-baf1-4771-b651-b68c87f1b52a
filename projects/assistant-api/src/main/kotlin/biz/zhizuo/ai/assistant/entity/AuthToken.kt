package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 认证令牌实体
 */
@Entity
@Table()
@EntityListeners(AuditingEntityListener::class)
data class AuthToken(
    @Id
    val id: String = randomId(),

    @Column()
    val userId: String,

    @Column(unique = true)
    val token: String,

    @Column()
    val expiresAt: LocalDateTime,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
)
