package biz.zhizuo.ai.assistant.entity

import biz.zhizuo.ai.assistant.utils.randomId
import jakarta.persistence.*
import org.hibernate.annotations.Comment
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

/**
 * 认证令牌实体
 */
@Entity
@Table()
@Comment("认证令牌实体")
@EntityListeners(AuditingEntityListener::class)
data class AuthToken(
    @Id
    val id: String = randomId(),

    @Column()
    @Comment("关联的用户ID")
    val userId: String,

    @Column(unique = true)
    @Comment("认证令牌值")
    val token: String,

    @Column()
    @Comment("令牌过期时间")
    val expiresAt: LocalDateTime,

    @CreatedDate
    @Column(updatable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updatedAt: LocalDateTime? = null,
)
